<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resource Permission Tester</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8" x-data="permissionTester()">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">🧪 Resource Permission Tester</h1>
                
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        {{ session('success') }}
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {{ session('error') }}
                    </div>
                @endif

                <!-- Current User Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Current Test Session</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-blue-700 font-medium">User:</span>
                            <span class="text-blue-900">{{ $currentUser->name }} ({{ $currentUser->email }})</span>
                        </div>
                        <div>
                            <span class="text-blue-700 font-medium">Roles:</span>
                            <span class="text-blue-900">{{ $currentUser->roles->pluck('name')->join(', ') }}</span>
                        </div>
                        <div>
                            <span class="text-blue-700 font-medium">Team:</span>
                            <span class="text-blue-900">{{ $currentTeam ? $currentTeam->name : 'No Team (Super Admin)' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- User Switcher -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">👤 Switch User/Role</h2>
                    
                    <form method="POST" action="{{ route('test.switch-user') }}" class="space-y-4">
                        @csrf
                        
                        <!-- Team Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Team</label>
                            <select name="team_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" x-model="selectedTeam" @change="updateUsers()">
                                <option value="">Super Admin (No Team)</option>
                                @foreach($teams as $team)
                                    <option value="{{ $team->id }}">{{ $team->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- User Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">User</label>
                            <select name="user_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                                <option value="">Select a user...</option>
                                <template x-for="user in filteredUsers" :key="user.id">
                                    <option :value="user.id" x-text="`${user.name} (${user.roles.map(r => r.name).join(', ')})`"></option>
                                </template>
                            </select>
                        </div>

                        <div class="flex space-x-2">
                            <button type="submit" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Switch User
                            </button>
                            
                            @if(session('original_user_id'))
                                <a href="{{ route('test.switch-back') }}" class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-center">
                                    Switch Back
                                </a>
                            @endif
                        </div>
                    </form>
                </div>

                <!-- Resource Testing -->
                <div class="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">🔒 Test Resources</h2>
                        <button @click="testAllResources()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                            Test All Resources
                        </button>
                    </div>

                    <!-- Resource List -->
                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        @foreach($resources as $resource)
                            <div class="border border-gray-200 rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-medium text-gray-900">{{ class_basename($resource) }}</h3>
                                        <p class="text-sm text-gray-500">{{ $resource }}</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button @click="testResource('{{ $resource }}')" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                            Test
                                        </button>
                                        <button @click="getResourceUrls('{{ $resource }}')" class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700">
                                            URLs
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Results -->
                                <div x-show="results['{{ class_basename($resource) }}']" class="mt-3 p-3 bg-gray-50 rounded">
                                    <template x-if="results['{{ class_basename($resource) }}']">
                                        <div>
                                            <div class="text-sm font-medium text-gray-700 mb-2">Permissions:</div>
                                            <div class="grid grid-cols-2 gap-2 text-xs">
                                                <template x-for="(allowed, permission) in results['{{ class_basename($resource) }}'].permissions || {}" :key="permission">
                                                    <div class="flex items-center space-x-1">
                                                        <span :class="allowed ? 'text-green-600' : 'text-red-600'" x-text="allowed ? '✅' : '❌'"></span>
                                                        <span x-text="permission"></span>
                                                    </div>
                                                </template>
                                            </div>
                                            
                                            <!-- URLs -->
                                            <template x-if="results['{{ class_basename($resource) }}'].urls">
                                                <div class="mt-3">
                                                    <div class="text-sm font-medium text-gray-700 mb-2">Available URLs:</div>
                                                    <div class="space-y-1">
                                                        <template x-for="(url, action) in results['{{ class_basename($resource) }}'].urls" :key="action">
                                                            <div class="flex items-center space-x-2">
                                                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded" x-text="action"></span>
                                                                <a :href="url" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 truncate" x-text="url"></a>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function permissionTester() {
            return {
                selectedTeam: '',
                teams: @json($teams),
                filteredUsers: [],
                results: {},
                
                init() {
                    this.updateUsers();
                },
                
                updateUsers() {
                    if (this.selectedTeam === '') {
                        // Super admin users (no team)
                        this.filteredUsers = this.teams.flatMap(team => team.users)
                            .filter(user => user.team_id === null);
                    } else {
                        // Users from selected team
                        const team = this.teams.find(t => t.id == this.selectedTeam);
                        this.filteredUsers = team ? team.users : [];
                    }
                },
                
                async testResource(resourceClass) {
                    try {
                        const response = await fetch('/test/resource', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({ resource: resourceClass })
                        });
                        
                        const data = await response.json();
                        if (data.success) {
                            this.results[data.resource] = data;
                        } else {
                            alert('Error: ' + data.error);
                        }
                    } catch (error) {
                        alert('Error testing resource: ' + error.message);
                    }
                },
                
                async testAllResources() {
                    try {
                        const response = await fetch('/test/all-resources');
                        const data = await response.json();
                        
                        if (data.success) {
                            this.results = {};
                            Object.keys(data.results).forEach(resource => {
                                this.results[resource] = {
                                    permissions: data.results[resource],
                                    user: data.user,
                                    team: data.team
                                };
                            });
                        }
                    } catch (error) {
                        alert('Error testing all resources: ' + error.message);
                    }
                },
                
                async getResourceUrls(resourceClass) {
                    try {
                        const response = await fetch('/test/resource-urls', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({ resource: resourceClass })
                        });
                        
                        const data = await response.json();
                        if (data.success) {
                            if (!this.results[data.resource]) {
                                this.results[data.resource] = {};
                            }
                            this.results[data.resource].urls = data.urls;
                        } else {
                            alert('Error: ' + data.error);
                        }
                    } catch (error) {
                        alert('Error getting resource URLs: ' + error.message);
                    }
                }
            }
        }
    </script>
</body>
</html>
