[2025-06-29 09:50:56] local.ERROR: syntax error, unexpected token "->", expecting "]" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"->\", expecting \"]\" at C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Filament\\Resources\\Exam\\ExamResource.php:180)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Projects\\\\Web...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Filament\\\\Re...')
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(438): class_exists('App\\\\Filament\\\\Re...')
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php(293): Filament\\Panel->discoverComponents('Filament\\\\Resour...', Array, 'C:\\\\Projects\\\\Web...', Object(Illuminate\\Support\\Stringable))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\app\\Providers\\Filament\\AdminPanelProvider.php(46): Filament\\Panel->discoverResources('C:\\\\Projects\\\\Web...', 'App\\\\Filament\\\\Re...')
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(89): Illuminate\\Support\\Facades\\Facade::__callStatic('serving', Array)
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(163): Filament\\FilamentServiceProvider->packageBooted()
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#35 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#44 {main}
"} 
