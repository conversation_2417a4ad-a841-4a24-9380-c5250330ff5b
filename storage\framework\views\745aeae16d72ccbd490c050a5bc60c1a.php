<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['class' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['class' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->merge(['class' => 'relative language-switcher ' . $class])); ?> x-data="{ open: false }">
    <button @click="open = !open" @click.away="open = false"
            @keydown.escape="open = false"
            :aria-expanded="open"
            aria-haspopup="true"
            class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300"
            id="language-menu-button">
        
        <!-- Current Language Flag -->
        <?php if(app()->getLocale() === 'th'): ?>
            <svg class="w-5 h-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Thai Flag -->
                <rect width="20" height="20" rx="2" fill="#ED1C24"/>
                <rect y="6" width="20" height="2" fill="#FFFFFF"/>
                <rect y="8" width="20" height="4" fill="#241D4F"/>
                <rect y="12" width="20" height="2" fill="#FFFFFF"/>
            </svg>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">ไทย</span>
        <?php else: ?>
            <svg class="w-5 h-5" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- US Flag -->
                <rect width="20" height="20" rx="2" fill="#B22234"/>
                <rect y="1" width="20" height="1" fill="#FFFFFF"/>
                <rect y="3" width="20" height="1" fill="#FFFFFF"/>
                <rect y="5" width="20" height="1" fill="#FFFFFF"/>
                <rect y="7" width="20" height="1" fill="#FFFFFF"/>
                <rect y="9" width="20" height="1" fill="#FFFFFF"/>
                <rect y="11" width="20" height="1" fill="#FFFFFF"/>
                <rect y="13" width="20" height="1" fill="#FFFFFF"/>
                <rect width="8" height="8" fill="#3C3B6E"/>
            </svg>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">EN</span>
        <?php endif; ?>
        
        <svg class="w-4 h-4 text-gray-500 transition-transform" :class="{ 'rotate-180': open }"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- Dropdown Menu -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         @keydown.escape="open = false"
         role="menu"
         aria-orientation="vertical"
         aria-labelledby="language-menu-button"
         class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50"
         style="display: none;"
         x-cloak>

        <!-- English Option -->
        <a href="<?php echo e(route('language.switch', 'en')); ?>"
           role="menuitem"
           class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors <?php echo e(app()->getLocale() === 'en' ? 'bg-gray-50 dark:bg-gray-700' : ''); ?>">
            <svg class="w-5 h-5 mr-3" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- US Flag -->
                <rect width="20" height="20" rx="2" fill="#B22234"/>
                <rect y="1" width="20" height="1" fill="#FFFFFF"/>
                <rect y="3" width="20" height="1" fill="#FFFFFF"/>
                <rect y="5" width="20" height="1" fill="#FFFFFF"/>
                <rect y="7" width="20" height="1" fill="#FFFFFF"/>
                <rect y="9" width="20" height="1" fill="#FFFFFF"/>
                <rect y="11" width="20" height="1" fill="#FFFFFF"/>
                <rect y="13" width="20" height="1" fill="#FFFFFF"/>
                <rect width="8" height="8" fill="#3C3B6E"/>
            </svg>
            <div>
                <div class="font-medium">English</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">EN</div>
            </div>
            <?php if(app()->getLocale() === 'en'): ?>
                <svg class="w-4 h-4 ml-auto text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            <?php endif; ?>
        </a>

        <!-- Thai Option -->
        <a href="<?php echo e(route('language.switch', 'th')); ?>"
           role="menuitem"
           class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors <?php echo e(app()->getLocale() === 'th' ? 'bg-gray-50 dark:bg-gray-700' : ''); ?>">
            <svg class="w-5 h-5 mr-3" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Thai Flag -->
                <rect width="20" height="20" rx="2" fill="#ED1C24"/>
                <rect y="6" width="20" height="2" fill="#FFFFFF"/>
                <rect y="8" width="20" height="4" fill="#241D4F"/>
                <rect y="12" width="20" height="2" fill="#FFFFFF"/>
            </svg>
            <div>
                <div class="font-medium">ไทย</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">TH</div>
            </div>
            <?php if(app()->getLocale() === 'th'): ?>
                <svg class="w-4 h-4 ml-auto text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            <?php endif; ?>
        </a>
    </div>
</div>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/components/language-switcher.blade.php ENDPATH**/ ?>