<?php

namespace App\Models\Exam;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Attempt extends Model
{
    use HasFactory;

    protected $table = 'exam_attempts';

    protected $fillable = [
        'team_id',
        'exam_id',
        'user_id',
        'attempt_number',
        'score_auto',
        'score_manual',
        'total_score',
        'status',
        'started_at',
        'submitted_at',
        'graded_at',
        'graded_by',
        'teacher_feedback',
        'exam_snapshot',
    ];

    protected $casts = [
        'score_auto' => 'float',
        'score_manual' => 'float',
        'total_score' => 'float',
        'started_at' => 'datetime',
        'submitted_at' => 'datetime',
        'graded_at' => 'datetime',
        'exam_snapshot' => 'array',
    ];

    // Relationships
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function grader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class, 'attempt_id');
    }

    // Scopes
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeSubmitted($query)
    {
        return $query->whereIn('status', ['submitted', 'graded']);
    }

    public function scopeNeedsGrading($query)
    {
        return $query->where('status', 'submitted')
            ->whereHas('answers', function ($q) {
                $q->whereHas('question', function ($qq) {
                    $qq->whereIn('question_type', ['short_answer', 'essay']);
                })->whereNull('is_correct');
            });
    }

    // Helper methods
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    public function isSubmitted(): bool
    {
        return in_array($this->status, ['submitted', 'graded']);
    }

    public function isGraded(): bool
    {
        return $this->status === 'graded';
    }

    public function calculateAutoScore(): float
    {
        $autoScore = $this->answers()
            ->whereHas('question', function ($q) {
                $q->where('question_type', 'multiple_choice');
            })
            ->where('is_correct', true)
            ->sum('score');

        return $autoScore;
    }

    public function calculateTotalScore(): float
    {
        $autoScore = $this->score_auto ?? 0;
        $manualScore = $this->score_manual ?? 0;

        return $autoScore + $manualScore;
    }

    public function updateScores(): void
    {
        $this->score_auto = $this->calculateAutoScore();
        $this->total_score = $this->calculateTotalScore();
        $this->save();
    }
}
