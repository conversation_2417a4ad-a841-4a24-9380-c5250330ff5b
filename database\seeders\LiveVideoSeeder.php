<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LiveVideo;
use App\Models\Course;
use App\Models\Book;
use App\Models\Team;
use App\Models\User;
use Carbon\Carbon;

class LiveVideoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = Team::all();

        foreach ($teams as $team) {
            // Get courses and books for this team
            $courses = Course::where('team_id', $team->id)->get();
            $books = Book::where('team_id', $team->id)->get();
            
            // Get teachers for this team
            $teachers = User::where('team_id', $team->id)
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'teacher');
                })
                ->get();

            if ($teachers->isEmpty()) {
                continue;
            }

            // Create live videos for courses
            foreach ($courses as $course) {
                $liveVideoCount = fake()->numberBetween(2, 5);

                for ($i = 1; $i <= $liveVideoCount; $i++) {
                    $this->createLiveVideo($team, $course, $teachers->random(), $i);
                }
            }

            // Create live videos for books
            foreach ($books as $book) {
                $liveVideoCount = fake()->numberBetween(1, 3);

                for ($i = 1; $i <= $liveVideoCount; $i++) {
                    $this->createLiveVideo($team, $book, $teachers->random(), $i);
                }
            }

            // Create some standalone live videos (not attached to course or book)
            $standaloneLiveVideoCount = fake()->numberBetween(3, 8);

            for ($i = 1; $i <= $standaloneLiveVideoCount; $i++) {
                $this->createLiveVideo($team, null, $teachers->random(), $i);
            }
        }
    }

    private function createLiveVideo(Team $team, $liveable, User $teacher, int $index): void
    {
        $status = collect(['scheduled', 'live', 'ended', 'cancelled'])->random();
        $scheduledAt = $this->getScheduledTime($status);
        
        $course = $liveable instanceof \App\Models\Course ? $liveable : null;
        $book = $liveable instanceof \App\Models\Book ? $liveable : null;

        $title = $this->generateTitle($course, $book, $index);
        $description = $this->generateDescription($course, $book, $title);

        $liveVideoData = [
            'team_id' => $team->id,
            'user_id' => $teacher->id,
            'title' => $title,
            'description' => $description,
            'scheduled_start_time' => $scheduledAt,
            'actual_start_time' => $status === 'live' ? now()->subMinutes(fake()->numberBetween(5, 60)) : null,
            'actual_end_time' => $status === 'ended' ? now()->subMinutes(fake()->numberBetween(5, 120)) : null,
            'status' => $status,
            'is_recording_enabled' => fake()->boolean(80),
            'is_public' => fake()->boolean(60),
            'is_active' => true,
            'stream_settings' => $this->generateSettings(),
            'created_at' => now()->subDays(fake()->numberBetween(1, 30)),
            'updated_at' => now()->subDays(fake()->numberBetween(0, 5)),
        ];

        // Add polymorphic relationship if liveable exists
        if ($liveable) {
            $liveVideoData['liveable_type'] = get_class($liveable);
            $liveVideoData['liveable_id'] = $liveable->id;
        }

        LiveVideo::create($liveVideoData);
    }

    private function getScheduledTime(string $status): Carbon
    {
        return match ($status) {
            'scheduled' => now()->addDays(fake()->numberBetween(1, 30))->addHours(fake()->numberBetween(1, 23)),
            'live' => now()->subMinutes(fake()->numberBetween(5, 60)),
            'ended' => now()->subDays(fake()->numberBetween(1, 7))->addHours(fake()->numberBetween(1, 23)),
            'cancelled' => now()->addDays(fake()->numberBetween(1, 15))->addHours(fake()->numberBetween(1, 23)),
            default => now()->addDays(1),
        };
    }

    private function getDuration(?string $status): ?int
    {
        return match ($status) {
            'ended' => fake()->numberBetween(30, 180),
            'live' => fake()->numberBetween(15, 120),
            default => fake()->numberBetween(45, 90),
        };
    }

    private function generateTitle(?Course $course, ?Book $book, int $index): string
    {
        if ($course) {
            return "Live Session {$index}: {$course->title}";
        }
        
        if ($book) {
            return "Book Discussion {$index}: {$book->title}";
        }

        $topics = [
            'Introduction to Advanced Mathematics',
            'Science Laboratory Techniques',
            'English Literature Analysis',
            'History and Social Studies',
            'Computer Programming Basics',
            'Art and Creative Expression',
            'Physical Education and Health',
            'Music Theory and Practice',
            'Environmental Science',
            'Business and Economics',
        ];

        return "Live Class {$index}: " . fake()->randomElement($topics);
    }

    private function generateDescription(?Course $course, ?Book $book, string $title): string
    {
        $baseDescription = "Join us for an interactive live session where we'll explore key concepts and engage in meaningful discussions.";
        
        if ($course) {
            return "{$baseDescription} This session is part of the {$course->title} course and will cover important topics related to the curriculum.";
        }
        
        if ($book) {
            return "{$baseDescription} We'll be discussing chapters from {$book->title} and analyzing key themes and concepts.";
        }

        return "{$baseDescription} This standalone session covers {$title} with practical examples and Q&A opportunities.";
    }

    private function generateMeetingUrl(): string
    {
        $platforms = ['zoom', 'teams', 'meet', 'webex'];
        $platform = fake()->randomElement($platforms);
        
        return match ($platform) {
            'zoom' => 'https://zoom.us/j/' . fake()->numerify('###########'),
            'teams' => 'https://teams.microsoft.com/l/meetup-join/' . fake()->uuid(),
            'meet' => 'https://meet.google.com/' . fake()->lexify('???-????-???'),
            'webex' => 'https://webex.com/meet/' . fake()->lexify('??????????'),
            default => 'https://zoom.us/j/' . fake()->numerify('###########'),
        };
    }

    private function generateMeetingId(): string
    {
        return fake()->numerify('###-###-###');
    }

    private function generateRecordingUrl(): string
    {
        return 'https://recordings.example.com/' . fake()->uuid() . '.mp4';
    }

    private function generateSettings(): array
    {
        return [
            'allow_chat' => fake()->boolean(90),
            'allow_screen_share' => fake()->boolean(80),
            'allow_recording' => fake()->boolean(85),
            'mute_participants_on_join' => fake()->boolean(70),
            'waiting_room_enabled' => fake()->boolean(60),
            'auto_record' => fake()->boolean(50),
            'breakout_rooms_enabled' => fake()->boolean(30),
            'whiteboard_enabled' => fake()->boolean(40),
            'polls_enabled' => fake()->boolean(35),
            'hand_raise_enabled' => fake()->boolean(95),
            'private_chat_enabled' => fake()->boolean(75),
            'file_sharing_enabled' => fake()->boolean(60),
            'annotation_enabled' => fake()->boolean(45),
            'virtual_background_enabled' => fake()->boolean(80),
            'blur_background_enabled' => fake()->boolean(70),
        ];
    }
}
