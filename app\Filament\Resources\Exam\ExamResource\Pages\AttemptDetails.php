<?php

namespace App\Filament\Resources\Exam\ExamResource\Pages;

use App\Filament\Resources\Exam\ExamResource;
use App\Models\Exam\Exam;
use App\Models\Exam\StudentExamAttempt;
use App\Models\User;
use Filament\Resources\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class AttemptDetails extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = ExamResource::class;

    protected static string $view = 'filament.resources.exam.exam-resource.pages.attempt-details';

    public Exam $exam;
    public User $user;
    public $attempts;
    public $activeTab = 0;
    public $data = [];

    public function mount(int $exam, int $user): void
    {
        $this->exam = Exam::where('team_id', filament()->getTenant()->id)->findOrFail($exam);
        $this->user = User::where('team_id', filament()->getTenant()->id)->findOrFail($user);
        
        $this->attempts = StudentExamAttempt::where('exam_id', $this->exam->id)
            ->where('user_id', $this->user->id)
            ->where('team_id', filament()->getTenant()->id)
            ->with(['answers.question.choices', 'answers.selectedChoice'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Initialize form data for each attempt
        foreach ($this->attempts as $index => $attempt) {
            $this->data[$index] = [
                'teacher_feedback' => $attempt->teacher_feedback,
                'answers' => []
            ];
            
            foreach ($attempt->answers as $answer) {
                $this->data[$index]['answers'][$answer->id] = [
                    'score' => $answer->score,
                    'teacher_comment' => $answer->teacher_comment,
                    'is_correct' => $answer->is_correct,
                ];
            }
        }
    }

    public function getTitle(): string
    {
        return "Attempt Details: {$this->user->name} - {$this->exam->title}";
    }

    public function form(Form $form): Form
    {
        $attempt = $this->attempts[$this->activeTab] ?? null;
        
        if (!$attempt) {
            return $form->schema([]);
        }

        $schema = [
            Forms\Components\Section::make('Overall Feedback')
                ->schema([
                    Forms\Components\Textarea::make("teacher_feedback")
                        ->label('Teacher Feedback')
                        ->rows(3)
                        ->placeholder('Provide overall feedback for this attempt...')
                ])
        ];

        // Add sections for each question
        foreach ($attempt->answers as $answer) {
            $schema[] = Forms\Components\Section::make("Question {$answer->question->sort_order}: {$answer->question->question_text}")
                ->schema([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\Placeholder::make('student_answer')
                                ->label('Student Answer')
                                ->content(function () use ($answer) {
                                    if ($answer->question->question_type === 'multiple_choice') {
                                        return $answer->selectedChoice?->choice_text ?? 'No answer selected';
                                    }
                                    return $answer->answer_text ?? 'No answer provided';
                                }),
                            
                            Forms\Components\Placeholder::make('correct_answer')
                                ->label('Correct Answer')
                                ->content(function () use ($answer) {
                                    if ($answer->question->question_type === 'multiple_choice') {
                                        $correctChoices = $answer->question->choices->where('is_correct', true);
                                        return $correctChoices->pluck('choice_text')->join(', ');
                                    }
                                    return 'Manual grading required';
                                })
                                ->visible(fn () => $answer->question->question_type === 'multiple_choice'),
                        ]),
                    
                    Forms\Components\Grid::make(3)
                        ->schema([
                            Forms\Components\TextInput::make("answers.{$answer->id}.score")
                                ->label('Score')
                                ->numeric()
                                ->minValue(0)
                                ->maxValue($answer->max_score)
                                ->suffix("/ {$answer->max_score}"),
                            
                            Forms\Components\Select::make("answers.{$answer->id}.is_correct")
                                ->label('Correct?')
                                ->options([
                                    1 => 'Correct',
                                    0 => 'Incorrect',
                                ])
                                ->placeholder('Not graded'),
                            
                            Forms\Components\Textarea::make("answers.{$answer->id}.teacher_comment")
                                ->label('Comment')
                                ->rows(2)
                                ->placeholder('Optional comment for this answer...')
                        ])
                ])
                ->collapsible();
        }

        return $form->schema($schema);
    }

    public function save(): void
    {
        $attempt = $this->attempts[$this->activeTab] ?? null;
        
        if (!$attempt) {
            return;
        }

        $data = $this->form->getState();

        DB::transaction(function () use ($attempt, $data) {
            // Update attempt feedback
            $attempt->update([
                'teacher_feedback' => $data['teacher_feedback'] ?? null,
                'graded_by' => auth()->id(),
                'graded_at' => now(),
                'status' => 'graded'
            ]);

            // Update individual answers
            if (isset($data['answers'])) {
                foreach ($data['answers'] as $answerId => $answerData) {
                    $answer = $attempt->answers()->find($answerId);
                    if ($answer) {
                        $answer->update([
                            'score' => $answerData['score'] ?? null,
                            'teacher_comment' => $answerData['teacher_comment'] ?? null,
                            'is_correct' => $answerData['is_correct'] ?? null,
                        ]);
                    }
                }
            }

            // Recalculate scores
            $attempt->updateScores();
        });

        Notification::make()
            ->title('Grading saved successfully')
            ->success()
            ->send();

        // Refresh the attempts data
        $this->mount($this->exam->id, $this->user->id);
    }

    public function setActiveTab(int $tab): void
    {
        $this->activeTab = $tab;
        $this->form->fill($this->data[$tab] ?? []);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('back')
                ->label('Back to Attempts')
                ->icon('heroicon-o-arrow-left')
                ->url(ExamResource::getUrl('view-attempts', ['record' => $this->exam])),
        ];
    }

    protected function getFormActions(): array
    {
        return [
            \Filament\Actions\Action::make('save')
                ->label('Save Grading')
                ->action('save')
                ->color('success'),
        ];
    }
}
