<?php

namespace App\Filament\Resources\Exam\ExamResource\Pages;

use App\Filament\Resources\Exam\ExamResource;
use App\Models\Exam\Exam;
use App\Models\Exam\Attempt;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ViewAttempts extends Page implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = ExamResource::class;

    protected static string $view = 'filament.resources.exam.exam-resource.pages.view-attempts';

    public Exam $record;

    public function mount(int | string $record): void
    {
        $this->record = Exam::findOrFail($record);
    }

    public function getTitle(): string
    {
        return "Attempts for: {$this->record->title}";
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Attempt::query()
                    ->where('exam_id', $this->record->id)
                    ->where('team_id', filament()->getTenant()->id)
                    ->with(['student', 'answers'])
                    ->select([
                        'user_id',
                        DB::raw('COUNT(*) as attempt_count'),
                        DB::raw('MAX(total_score) as max_score'),
                        DB::raw('MAX(id) as latest_attempt_id'),
                        DB::raw('MAX(created_at) as latest_attempt_date'),
                        DB::raw('(SELECT total_score FROM exam_attempts ea WHERE ea.user_id = exam_attempts.user_id AND ea.exam_id = exam_attempts.exam_id ORDER BY ea.created_at DESC LIMIT 1) as latest_score'),
                        DB::raw('(SELECT status FROM exam_attempts ea WHERE ea.user_id = exam_attempts.user_id AND ea.exam_id = exam_attempts.exam_id ORDER BY ea.created_at DESC LIMIT 1) as latest_status')
                    ])
                    ->groupBy('user_id')
            )
            ->columns([
                Tables\Columns\TextColumn::make('student.name')
                    ->label('Student')
                    ->searchable()
                    ->sortable()
                    ->getStateUsing(function ($record) {
                        return \App\Models\User::find($record->user_id)?->name ?? 'Unknown User';
                    }),

                Tables\Columns\TextColumn::make('student.email')
                    ->label('Email')
                    ->searchable()
                    ->getStateUsing(function ($record) {
                        return \App\Models\User::find($record->user_id)?->email ?? 'Unknown Email';
                    }),

                Tables\Columns\TextColumn::make('attempt_count')
                    ->label('Total Attempts')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('max_score')
                    ->label('Best Score')
                    ->alignCenter()
                    ->sortable()
                    ->formatStateUsing(fn ($state) => $state ? "{$state}/{$this->record->total_score}" : 'N/A'),

                Tables\Columns\TextColumn::make('latest_score')
                    ->label('Latest Score')
                    ->alignCenter()
                    ->formatStateUsing(fn ($state) => $state ? "{$state}/{$this->record->total_score}" : 'N/A'),

                Tables\Columns\TextColumn::make('latest_status')
                    ->label('Latest Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'in_progress' => 'warning',
                        'submitted' => 'info',
                        'graded' => 'success',
                        'expired' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('latest_attempt_date')
                    ->label('Latest Attempt')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->label('View Details')
                    ->icon('heroicon-o-eye')
                    ->url(function ($record) {
                        return route('filament.backend.resources.exams.attempt-details', [
                            'tenant' => filament()->getTenant(),
                            'exam' => $this->record->id,
                            'user' => $record->user_id
                        ]);
                    })
                    ->openUrlInNewTab(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('latest_status')
                    ->label('Status')
                    ->options([
                        'in_progress' => 'In Progress',
                        'submitted' => 'Submitted',
                        'graded' => 'Graded',
                        'expired' => 'Expired',
                    ]),
            ])
            ->defaultSort('latest_attempt_date', 'desc');
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('back')
                ->label('Back to Exam')
                ->icon('heroicon-o-arrow-left')
                ->url(ExamResource::getUrl('edit', ['record' => $this->record])),
        ];
    }
}
