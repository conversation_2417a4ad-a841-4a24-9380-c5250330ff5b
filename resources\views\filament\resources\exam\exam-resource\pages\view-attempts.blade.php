<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Exam Info Header -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-start justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">{{ $this->record->title }}</h2>
                    <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        @if($this->record->subject)
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{{ $this->record->subject->name }}</span>
                        @endif
                        @if($this->record->grade_level)
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full">{{ $this->record->grade_level }}</span>
                        @endif
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full capitalize">{{ $this->record->type }}</span>
                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full capitalize">{{ $this->record->visibility }}</span>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Total Score</div>
                    <div class="text-2xl font-bold text-gray-900">{{ $this->record->total_score }}</div>
                </div>
            </div>
            
            @if($this->record->description)
                <div class="mt-4">
                    <p class="text-gray-600">{{ $this->record->description }}</p>
                </div>
            @endif
            
            <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->record->total_questions }}</div>
                    <div class="text-xs text-gray-500">Questions</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->record->time_limit ?? '∞' }}</div>
                    <div class="text-xs text-gray-500">{{ $this->record->time_limit ? 'Minutes' : 'No Limit' }}</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->record->attempts()->count() }}</div>
                    <div class="text-xs text-gray-500">Total Attempts</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->record->attempts()->distinct('user_id')->count() }}</div>
                    <div class="text-xs text-gray-500">Unique Students</div>
                </div>
            </div>
        </div>

        <!-- Attempts Table -->
        <div class="bg-white rounded-lg border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Student Attempts</h3>
                <p class="mt-1 text-sm text-gray-500">
                    View and manage student attempts for this exam. Click "View Details" to see individual attempt details and provide feedback.
                </p>
            </div>
            
            <div class="p-6">
                {{ $this->table }}
            </div>
        </div>
    </div>
</x-filament-panels::page>
