<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Exam\Exam;
use App\Models\Exam\StudentExamAttempt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AssignmentController extends Controller
{
    /**
     * Display a listing of assignments for the current user
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        if (!$user || !$user->team_id) {
            return redirect()->route('login');
        }

        $query = Assignment::where('team_id', $user->team_id)
            ->where('is_active', true)
            ->with(['subject', 'teacher', 'exams', 'submissions' => function ($q) use ($user) {
                $q->where('user_id', $user->id);
            }]);

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'pending':
                    $query->whereDoesntHave('submissions', function ($q) use ($user) {
                        $q->where('user_id', $user->id);
                    });
                    break;
                case 'submitted':
                    $query->whereHas('submissions', function ($q) use ($user) {
                        $q->where('user_id', $user->id)->where('status', 'submitted');
                    });
                    break;
                case 'graded':
                    $query->whereHas('submissions', function ($q) use ($user) {
                        $q->where('user_id', $user->id)->where('status', 'graded');
                    });
                    break;
            }
        }

        // Filter by subject
        if ($request->filled('subject')) {
            $query->where('subject_id', $request->subject);
        }

        $assignments = $query->orderBy('due_date', 'asc')->paginate(12);

        // Get subjects for filter
        $subjects = \App\Models\Subject::where('team_id', $user->team_id)
            ->whereHas('assignments', function ($q) use ($user) {
                $q->where('team_id', $user->team_id)->where('is_active', true);
            })
            ->get();

        return view('frontend.assignments.index', compact('assignments', 'subjects'));
    }

    /**
     * Display the specified assignment
     */
    public function show(Assignment $assignment)
    {
        $user = Auth::user();
        
        if (!$user || $assignment->team_id !== $user->team_id) {
            abort(404);
        }

        $assignment->load(['subject', 'teacher', 'exams.subject']);

        // Get user's submission
        $submission = $assignment->submissions()->where('user_id', $user->id)->first();

        // Get exam completion status
        $examCompletions = [];
        foreach ($assignment->exams as $exam) {
            $attempts = $exam->attempts()
                ->where('user_id', $user->id)
                ->where('status', 'submitted')
                ->get();
            
            $examCompletions[$exam->id] = [
                'completed' => $attempts->count() > 0,
                'best_score' => $attempts->max('total_score'),
                'latest_attempt' => $attempts->sortByDesc('created_at')->first(),
                'attempt_count' => $attempts->count(),
            ];
        }

        return view('frontend.assignments.show', compact('assignment', 'submission', 'examCompletions'));
    }

    /**
     * Start working on an assignment
     */
    public function start(Assignment $assignment)
    {
        $user = Auth::user();
        
        if (!$user || $assignment->team_id !== $user->team_id) {
            abort(404);
        }

        // Check if already has a submission
        $submission = $assignment->submissions()->where('user_id', $user->id)->first();
        
        if (!$submission) {
            $submission = AssignmentSubmission::create([
                'team_id' => $assignment->team_id,
                'assignment_id' => $assignment->id,
                'user_id' => $user->id,
                'status' => AssignmentSubmission::STATUS_DRAFT,
            ]);
        }

        return redirect()->route('frontend.assignments.work', $assignment);
    }

    /**
     * Work on assignment (complete exams, submit content)
     */
    public function work(Assignment $assignment)
    {
        $user = Auth::user();
        
        if (!$user || $assignment->team_id !== $user->team_id) {
            abort(404);
        }

        $assignment->load(['subject', 'teacher', 'exams']);

        // Get or create submission
        $submission = $assignment->submissions()->where('user_id', $user->id)->first();
        
        if (!$submission) {
            $submission = AssignmentSubmission::create([
                'team_id' => $assignment->team_id,
                'assignment_id' => $assignment->id,
                'user_id' => $user->id,
                'status' => AssignmentSubmission::STATUS_DRAFT,
            ]);
        }

        // Check if already submitted
        if ($submission->status === AssignmentSubmission::STATUS_SUBMITTED) {
            return redirect()->route('frontend.assignments.show', $assignment)
                ->with('info', 'This assignment has already been submitted.');
        }

        // Get exam completion status
        $examCompletions = [];
        foreach ($assignment->exams as $exam) {
            $attempts = $exam->attempts()
                ->where('user_id', $user->id)
                ->where('status', 'submitted')
                ->get();
            
            $examCompletions[$exam->id] = [
                'completed' => $attempts->count() > 0,
                'best_score' => $attempts->max('total_score'),
                'latest_attempt' => $attempts->sortByDesc('created_at')->first(),
                'attempt_count' => $attempts->count(),
            ];
        }

        return view('frontend.assignments.work', compact('assignment', 'submission', 'examCompletions'));
    }

    /**
     * Submit assignment
     */
    public function submit(Request $request, Assignment $assignment)
    {
        $user = Auth::user();
        
        if (!$user || $assignment->team_id !== $user->team_id) {
            abort(404);
        }

        $submission = $assignment->submissions()->where('user_id', $user->id)->first();
        
        if (!$submission) {
            return redirect()->route('frontend.assignments.show', $assignment)
                ->with('error', 'No submission found. Please start the assignment first.');
        }

        if ($submission->status === AssignmentSubmission::STATUS_SUBMITTED) {
            return redirect()->route('frontend.assignments.show', $assignment)
                ->with('info', 'This assignment has already been submitted.');
        }

        $request->validate([
            'content' => 'nullable|string',
            'file' => 'nullable|file|max:10240', // 10MB max
        ]);

        DB::transaction(function () use ($request, $submission) {
            $updateData = [
                'content' => $request->input('content'),
                'status' => AssignmentSubmission::STATUS_SUBMITTED,
                'submitted_at' => now(),
            ];

            // Handle file upload
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $path = $file->store('assignment-submissions', 'public');
                $updateData['file_path'] = $path;
            }

            $submission->update($updateData);
        });

        return redirect()->route('frontend.assignments.show', $assignment)
            ->with('success', 'Assignment submitted successfully!');
    }
}
