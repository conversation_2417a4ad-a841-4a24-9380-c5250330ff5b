


<?php if($type === 'floating-particles'): ?>
<div class="floating-particles">
    <?php for($i = 1; $i <= 9; $i++): ?>
    <div class="particle" style="
        width: <?php echo e(rand(4, 12)); ?>px; 
        height: <?php echo e(rand(4, 12)); ?>px; 
        left: <?php echo e(rand(0, 100)); ?>%; 
        animation-delay: <?php echo e(rand(0, 5)); ?>s;
        animation-duration: <?php echo e(rand(6, 10)); ?>s;
    "></div>
    <?php endfor; ?>
</div>
<?php endif; ?>


<?php if($type === 'wave-divider'): ?>
<svg class="w-full h-16" viewBox="0 0 1200 120" preserveAspectRatio="none">
    <path class="wave-svg" d="M0,50 Q300,10 600,50 T1200,50 L1200,120 L0,120 Z" 
          fill="<?php echo e($color ?? 'rgba(6, 182, 212, 0.1)'); ?>"/>
</svg>
<?php endif; ?>


<?php if($type === 'pulsing-orb'): ?>
<svg class="w-16 h-16 pulse-glow-svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="30" 
            fill="<?php echo e($color ?? '#06b6d4'); ?>" 
            opacity="0.7"/>
    <circle cx="50" cy="50" r="20" 
            fill="<?php echo e($color ?? '#3b82f6'); ?>" 
            opacity="0.9"/>
    <circle cx="50" cy="50" r="10" 
            fill="white" 
            opacity="1"/>
</svg>
<?php endif; ?>


<?php if($type === 'orbital-system'): ?>
<div class="relative w-32 h-32">
    <svg class="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        <!-- Central orb -->
        <circle cx="50" cy="50" r="8" fill="<?php echo e($color ?? '#06b6d4'); ?>" class="pulse-glow-svg"/>
        
        <!-- Orbital rings -->
        <circle cx="50" cy="50" r="25" fill="none" stroke="<?php echo e($color ?? '#06b6d4'); ?>" 
                stroke-width="1" opacity="0.3"/>
        <circle cx="50" cy="50" r="35" fill="none" stroke="<?php echo e($color ?? '#3b82f6'); ?>" 
                stroke-width="1" opacity="0.2"/>
        
        <!-- Orbiting particles -->
        <g class="orbital-animation" style="animation-duration: 4s;">
            <circle cx="75" cy="50" r="3" fill="<?php echo e($color ?? '#8b5cf6'); ?>"/>
        </g>
        <g class="orbital-animation" style="animation-duration: 6s; animation-direction: reverse;">
            <circle cx="85" cy="50" r="2" fill="<?php echo e($color ?? '#ec4899'); ?>"/>
        </g>
    </svg>
</div>
<?php endif; ?>


<?php if($type === 'morphing-blob'): ?>
<svg class="w-24 h-24" viewBox="0 0 100 100">
    <path class="morph-shape" 
          fill="<?php echo e($color ?? 'rgba(6, 182, 212, 0.3)'); ?>"
          d="M20,20 C20,20 50,20 50,20 C80,20 80,50 80,50 C80,80 50,80 50,80 C20,80 20,50 20,50 Z"/>
</svg>
<?php endif; ?>


<?php if($type === 'dna-helix'): ?>
<svg class="w-16 h-32 dna-helix" viewBox="0 0 50 100">
    <path d="M10,10 Q25,25 40,40 Q25,55 10,70 Q25,85 40,100" 
          stroke="<?php echo e($color ?? '#06b6d4'); ?>" stroke-width="2" fill="none"/>
    <path d="M40,10 Q25,25 10,40 Q25,55 40,70 Q25,85 10,100" 
          stroke="<?php echo e($color ?? '#3b82f6'); ?>" stroke-width="2" fill="none"/>
    
    <!-- Connection lines -->
    <?php for($i = 0; $i < 5; $i++): ?>
    <line x1="10" y1="<?php echo e(20 + $i * 20); ?>" x2="40" y2="<?php echo e(20 + $i * 20); ?>" 
          stroke="<?php echo e($color ?? '#8b5cf6'); ?>" stroke-width="1" opacity="0.6"/>
    <?php endfor; ?>
</svg>
<?php endif; ?>


<?php if($type === 'geometric-spinner'): ?>
<svg class="w-16 h-16 spiral-svg" viewBox="0 0 100 100">
    <polygon points="50,15 65,35 85,35 70,50 75,70 50,60 25,70 30,50 15,35 35,35" 
             fill="<?php echo e($color ?? '#06b6d4'); ?>" opacity="0.8"/>
    <polygon points="50,25 60,40 75,40 65,50 70,65 50,55 30,65 35,50 25,40 40,40" 
             fill="<?php echo e($color ?? '#3b82f6'); ?>" opacity="0.6"/>
</svg>
<?php endif; ?>


<?php if($type === 'heartbeat'): ?>
<svg class="w-32 h-16 heartbeat-svg" viewBox="0 0 200 80">
    <path d="M10,40 L30,40 L35,20 L40,60 L45,30 L50,50 L55,40 L190,40" 
          stroke="<?php echo e($color ?? '#06b6d4'); ?>" stroke-width="2" fill="none" class="draw-svg"/>
    <circle cx="100" cy="40" r="3" fill="<?php echo e($color ?? '#ec4899'); ?>" class="pulse-glow-svg"/>
</svg>
<?php endif; ?>


<?php if($type === 'liquid-loading'): ?>
<div class="relative w-16 h-16">
    <svg class="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="45" fill="none" stroke="<?php echo e($color ?? '#e5e7eb'); ?>" stroke-width="4"/>
        <circle cx="50" cy="50" r="45" fill="none" stroke="<?php echo e($color ?? '#06b6d4'); ?>" 
                stroke-width="4" stroke-linecap="round" 
                stroke-dasharray="283" stroke-dashoffset="75"
                class="spin-animation" style="animation-duration: 2s;"/>
    </svg>
    <div class="absolute inset-2 liquid-blob" 
         style="background: <?php echo e($color ?? 'rgba(6, 182, 212, 0.2)'); ?>;"></div>
</div>
<?php endif; ?>


<?php if($type === 'kaleidoscope'): ?>
<svg class="w-24 h-24 kaleidoscope-svg" viewBox="0 0 100 100">
    <g transform="translate(50,50)">
        <?php for($i = 0; $i < 6; $i++): ?>
        <g transform="rotate(<?php echo e($i * 60); ?>)">
            <polygon points="0,0 20,10 15,25 5,20" 
                     fill="<?php echo e($color ?? '#06b6d4'); ?>" opacity="0.7"/>
            <polygon points="0,0 15,15 10,30 0,25" 
                     fill="<?php echo e($color ?? '#3b82f6'); ?>" opacity="0.5"/>
        </g>
        <?php endfor; ?>
    </g>
</svg>
<?php endif; ?>


<?php if($type === 'magnetic-field'): ?>
<svg class="w-32 h-24" viewBox="0 0 200 120">
    <?php for($i = 0; $i < 5; $i++): ?>
    <path d="M20,<?php echo e(20 + $i * 20); ?> Q100,<?php echo e(10 + $i * 20); ?> 180,<?php echo e(20 + $i * 20); ?>" 
          stroke="<?php echo e($color ?? '#06b6d4'); ?>" stroke-width="1" fill="none" 
          class="magnetic-field" style="animation-delay: <?php echo e($i * 0.2); ?>s;"/>
    <?php endfor; ?>
</svg>
<?php endif; ?>


<?php if($type === 'pendulum'): ?>
<svg class="w-16 h-24" viewBox="0 0 60 100">
    <line x1="30" y1="10" x2="30" y2="20" stroke="<?php echo e($color ?? '#64748b'); ?>" stroke-width="2"/>
    <g class="pendulum-svg" transform-origin="30 20">
        <line x1="30" y1="20" x2="30" y2="80" stroke="<?php echo e($color ?? '#06b6d4'); ?>" stroke-width="2"/>
        <circle cx="30" cy="80" r="8" fill="<?php echo e($color ?? '#3b82f6'); ?>"/>
    </g>
</svg>
<?php endif; ?>


<?php if($type === 'glitch-text'): ?>
<div class="relative">
    <svg class="w-32 h-8 glitch-svg" viewBox="0 0 200 40">
        <text x="100" y="25" text-anchor="middle" font-family="monospace" font-size="16" 
              fill="<?php echo e($color ?? '#06b6d4'); ?>"><?php echo e($text ?? 'LOADING'); ?></text>
    </svg>
</div>
<?php endif; ?>


<?php if($type === 'breathing-circle'): ?>
<svg class="w-20 h-20 breathing-svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="none" 
            stroke="<?php echo e($color ?? '#06b6d4'); ?>" stroke-width="2" opacity="0.8"/>
    <circle cx="50" cy="50" r="30" fill="none" 
            stroke="<?php echo e($color ?? '#3b82f6'); ?>" stroke-width="2" opacity="0.6"/>
    <circle cx="50" cy="50" r="20" fill="none" 
            stroke="<?php echo e($color ?? '#8b5cf6'); ?>" stroke-width="2" opacity="0.4"/>
    <circle cx="50" cy="50" r="10" 
            fill="<?php echo e($color ?? '#ec4899'); ?>" opacity="0.8"/>
</svg>
<?php endif; ?>


<?php if($type === 'elastic-bounce'): ?>
<svg class="w-12 h-12 elastic-bounce" viewBox="0 0 100 100">
    <rect x="25" y="25" width="50" height="50" rx="10"
          fill="<?php echo e($color ?? '#06b6d4'); ?>" opacity="0.8"/>
    <rect x="35" y="35" width="30" height="30" rx="5"
          fill="<?php echo e($color ?? '#3b82f6'); ?>" opacity="0.9"/>
    <circle cx="50" cy="50" r="8" fill="white"/>
</svg>
<?php endif; ?>


<?php if($type === 'custom-icon'): ?>
<div class="w-<?php echo e($size ?? '16'); ?> h-<?php echo e($size ?? '16'); ?> <?php echo e($animation ?? 'pulse-glow-svg'); ?>">
    <?php
        $iconName = $icon ?? 'default';
        $iconColor = $color ?? 'currentColor';
    ?>

    <?php if($iconName === 'default'): ?>
        <svg viewBox="0 0 24 24" fill="none" stroke="<?php echo e($iconColor); ?>" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <path d="m9 12 2 2 4-4"/>
        </svg>
    <?php elseif($iconName === 'education'): ?>
        <svg viewBox="0 0 24 24" fill="none" stroke="<?php echo e($iconColor); ?>" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
        </svg>
    <?php elseif($iconName === 'download'): ?>
        <svg viewBox="0 0 24 24" fill="none" stroke="<?php echo e($iconColor); ?>" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
    <?php else: ?>
        
        <?php
            $iconPath = 'components.svg.icons.' . $iconName;
            $iconExists = view()->exists($iconPath);
        ?>

        <?php if($iconExists): ?>
            <?php echo $__env->make($iconPath, ['color' => $iconColor], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php else: ?>
            
            <svg viewBox="0 0 24 24" fill="none" stroke="<?php echo e($iconColor); ?>" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="m9 12 2 2 4-4"/>
            </svg>
        <?php endif; ?>
    <?php endif; ?>
</div>
<?php endif; ?>


<?php if($type === 'external-svg'): ?>
<div class="w-<?php echo e($size ?? '16'); ?> h-<?php echo e($size ?? '16'); ?> <?php echo e($animation ?? ''); ?>">
    <img src="<?php echo e(asset('images/svg/' . ($file ?? 'default.svg'))); ?>"
         alt="<?php echo e($alt ?? 'SVG Icon'); ?>"
         class="w-full h-full"
         style="filter: <?php echo e($filter ?? 'none'); ?>;">
</div>
<?php endif; ?>


<?php if($type === 'inline-custom'): ?>
<svg class="w-<?php echo e($size ?? '16'); ?> h-<?php echo e($size ?? '16'); ?> <?php echo e($animation ?? ''); ?>"
     viewBox="<?php echo e($viewBox ?? '0 0 24 24'); ?>"
     fill="<?php echo e($fill ?? 'none'); ?>"
     stroke="<?php echo e($color ?? 'currentColor'); ?>">
    <?php echo $svgContent ?? '<circle cx="12" cy="12" r="10"/>'; ?>

</svg>
<?php endif; ?>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/components/animated-svg.blade.php ENDPATH**/ ?>