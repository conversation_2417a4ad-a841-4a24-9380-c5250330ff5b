<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exam_answers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->foreignId('attempt_id')->constrained('exam_attempts')->cascadeOnDelete();
            $table->foreignId('question_id')->constrained('exam_questions')->cascadeOnDelete();
            $table->foreignId('selected_choice_id')->nullable()->constrained('exam_choices')->nullOnDelete(); // ถ้าเป็น multiple choice
            $table->text('answer_text')->nullable(); // ถ้าเป็นข้อเขียน
            $table->boolean('is_correct')->nullable(); // สำหรับคำตอบที่ตรวจแล้ว
            $table->float('score')->nullable(); // คะแนนแต่ละข้อ
            $table->float('max_score')->default(1); // คะแนนเต็มของข้อนี้
            $table->text('teacher_comment')->nullable(); // ความเห็นจากครูสำหรับข้อนี้
            $table->datetime('answered_at')->nullable(); // เวลาที่ตอบ
            $table->timestamps();

            $table->index(['team_id', 'attempt_id']);
            $table->index(['question_id', 'is_correct']);
            $table->unique(['attempt_id', 'question_id']); // ป้องกันการตอบซ้ำ
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exam_answers');
    }
};
