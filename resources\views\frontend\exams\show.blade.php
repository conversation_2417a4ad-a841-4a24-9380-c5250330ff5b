@extends('layouts.frontend')

@section('title', $exam->title)

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('frontend.exams.index') }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-800">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Exams
            </a>
        </div>

        <!-- Exam Header -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            <div class="flex items-start justify-between mb-6">
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $exam->title }}</h1>
                    <div class="flex items-center space-x-4 mb-4">
                        @if($exam->subject)
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">{{ $exam->subject->name }}</span>
                        @endif
                        @if($exam->grade_level)
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">{{ $exam->grade_level }}</span>
                        @endif
                        <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium capitalize">{{ $exam->type }}</span>
                    </div>
                </div>
            </div>

            @if($exam->description)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                    <p class="text-gray-600">{{ $exam->description }}</p>
                </div>
            @endif

            <!-- Exam Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ $exam->total_questions }}</div>
                    <div class="text-sm text-gray-500">Questions</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ $exam->total_score }}</div>
                    <div class="text-sm text-gray-500">Total Points</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">{{ $exam->time_limit ?? '∞' }}</div>
                    <div class="text-sm text-gray-500">{{ $exam->time_limit ? 'Minutes' : 'No Time Limit' }}</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">{{ $exam->allow_multiple_attempts ? 'Yes' : 'No' }}</div>
                    <div class="text-sm text-gray-500">Multiple Attempts</div>
                </div>
            </div>

            <!-- Teacher Info -->
            <div class="border-t pt-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-gray-700">{{ substr($exam->teacher->name, 0, 1) }}</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">Created by {{ $exam->teacher->name }}</div>
                        <div class="text-sm text-gray-500">{{ $exam->created_at->format('M d, Y') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Attempts (if logged in) -->
        @auth
            @if($userAttempts && $userAttempts->count() > 0)
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Your Previous Attempts</h3>
                    <div class="space-y-3">
                        @foreach($userAttempts as $attempt)
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="text-sm font-medium text-gray-900">Attempt #{{ $attempt->attempt_number }}</div>
                                    <div class="text-sm text-gray-500">{{ $attempt->created_at->format('M d, Y H:i') }}</div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($attempt->status === 'completed') bg-green-100 text-green-800
                                        @elseif($attempt->status === 'in_progress') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800
                                        @endif">
                                        {{ ucfirst($attempt->status) }}
                                    </span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    @if($attempt->total_score !== null)
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $attempt->total_score }}/{{ $exam->total_score }} pts
                                        </div>
                                    @endif
                                    @if($attempt->isSubmitted())
                                        <a href="{{ route('frontend.exams.result', [$exam, $attempt]) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            View Result
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @endauth

        <!-- Start Exam Section -->
        <div class="bg-white rounded-lg shadow-sm p-8">
            @auth
                @if(!$exam->allow_multiple_attempts && $userAttempts && $userAttempts->count() > 0)
                    <div class="text-center">
                        <div class="text-yellow-600 mb-4">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Already Completed</h3>
                        <p class="text-gray-600 mb-4">You have already taken this exam and multiple attempts are not allowed.</p>
                        <a href="{{ route('frontend.exams.result', [$exam, $userAttempts->first()]) }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            View Your Result
                        </a>
                    </div>
                @else
                    <div class="text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Ready to Start?</h3>
                        <p class="text-gray-600 mb-6">Make sure you have enough time to complete the exam before starting.</p>
                        
                        <form method="POST" action="{{ route('frontend.exams.start', $exam) }}">
                            @csrf
                            <button type="submit" 
                                    class="inline-flex items-center px-6 py-3 bg-green-600 text-white text-lg font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Start Exam
                            </button>
                        </form>
                    </div>
                @endif
            @else
                <div class="text-center">
                    <div class="text-blue-600 mb-4">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Login Required</h3>
                    <p class="text-gray-600 mb-6">You need to be logged in to take this exam.</p>
                    <div class="space-x-4">
                        <a href="{{ route('login') }}" 
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700">
                            Login
                        </a>
                        <a href="{{ route('register') }}" 
                           class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700">
                            Register
                        </a>
                    </div>
                </div>
            @endauth
        </div>
    </div>
</div>
@endsection
